import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter/material.dart';

class CycleSelectorSheet extends StatefulWidget {
  const CycleSelectorSheet({
    super.key,
    required this.type,
    required this.month,
    required this.currentCycle,
    required this.monthNames,
  });

  final NurtureType type;
  final int month;
  final int currentCycle;
  final List<String> monthNames;

  @override
  State<CycleSelectorSheet> createState() => _CycleSelectorSheetState();
}

class _CycleSelectorSheetState extends State<CycleSelectorSheet> {
  late int currentCycle;
  late final TextEditingController textEditingController;
  bool isCustomInputMode = false;
  String? validationError;

  @override
  void initState() {
    super.initState();
    currentCycle = widget.currentCycle;
    textEditingController = TextEditingController();
    isCustomInputMode = currentCycle > 0;
    if (currentCycle > 0) {
      textEditingController.text = currentCycle.toString();
    }
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${widget.type.name} - ${widget.monthNames[widget.month - 1]}",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // 继承选项
          _buildCycleOption(
            context: context,
            title: 'monthly_cycle_settings.inherit'.tr(),
            subtitle: 'monthly_cycle_settings.inherit_description'.tr(),
            value: 0,
            currentValue: currentCycle,
            onTap: _onInheritTap,
          ),

          // 未设置选项
          _buildCycleOption(
            context: context,
            title: 'monthly_cycle_settings.unset'.tr(),
            subtitle: 'monthly_cycle_settings.unset_description'.tr(),
            value: -1,
            currentValue: currentCycle,
            onTap: _onUnsetTap,
          ),

          // 自定义天数选项
          _buildCustomCycleOption(context),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCycleOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required int value,
    required int currentValue,
    required VoidCallback onTap,
  }) {
    final isSelected = currentValue == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCycleOption(BuildContext context) {
    final isCustom = currentCycle > 0;

    return GestureDetector(
      onTap: _onCustomCycleTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: _buildOptionDecoration(context, isCustom),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'monthly_cycle_settings.custom_days'.tr(),
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: isCustom ? Theme.of(context).primaryColor : Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isCustom && !isCustomInputMode)
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            _buildCustomInputSection(context),
          ],
        ),
      ),
    );
  }

  void _setCycle(BuildContext context, int cycle) {
    Navigator.pop(context, cycle);
  }

  void _onInheritTap() {
    _setCycle(context, 0);
  }

  void _onUnsetTap() {
    _setCycle(context, -1);
  }

  void _onCustomCycleTap() {
    setState(() {
      isCustomInputMode = true;
      validationError = null;
      if (currentCycle <= 0) {
        currentCycle = 1;
        textEditingController.text = '1';
      }
    });

    // 延迟聚焦以确保输入框已经构建
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  BoxDecoration _buildOptionDecoration(BuildContext context, bool isSelected) {
    return BoxDecoration(
      color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
      borderRadius: BorderRadius.circular(6),
      border: Border.all(
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
        width: isSelected ? 2 : 1,
      ),
    );
  }

  Widget _buildCustomInputSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: textEditingController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'monthly_cycle_settings.input_days_hint'.tr(),
                  // border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  hintStyle: TextStyle(color: Colors.grey[500]),
                  errorText: validationError,
                ),
                onChanged: _validateInput,
                onSubmitted: _onInputSubmitted,
                onTap: _onTapTextField
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                elevation: 0,
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: _onConfirmPressed,
              child: Text('confirm'.tr()),
            ),
          ],
        )
      ],
    );
  }

  void _validateInput(String value) {
    setState(() {
      if (value.isEmpty) {
        validationError = null;
        return;
      }

      final days = int.tryParse(value);
      if (days == null) {
        validationError = 'monthly_cycle_settings.invalid_days_error'.tr();
      } else if (days < 1 || days > 366) {
        validationError = 'monthly_cycle_settings.invalid_days_error'.tr();
      } else {
        validationError = null;
      }
    });
  }

  void _onInputSubmitted(String value) {
    if (validationError == null && value.isNotEmpty) {
      final days = int.tryParse(value);
      if (days != null && days >= 1 && days <= 366) {
        _setCycle(context, days);
      }
    }
  }

  void _onConfirmPressed() {
    final value = textEditingController.text;
    if (value.isEmpty) {
      setState(() {
        validationError = 'monthly_cycle_settings.invalid_days_error'.tr();
      });
      return;
    }

    final days = int.tryParse(value);
    if (days != null && days >= 1 && days <= 366) {
      _setCycle(context, days);
    } else {
      setState(() {
        validationError = 'monthly_cycle_settings.invalid_days_error'.tr();
      });
    }
  }

  void _onTapTextField() {
    setState(() {
      currentCycle = 1;
    });
  }
}
