import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

class FlowerMonthlyNurtureCycle {
  static const tableName = 'flower_monthly_nurture_cycle';

  final int id;
  final int flowerId;
  final int typeId;
  final int month; // 1-12
  final int cycle; // -1=未设置, 0=继承, >0=天数

  FlowerMonthlyNurtureCycle(this.id, this.flowerId, this.typeId, this.month, this.cycle);

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flower_id INTEGER, '
        'type_id INTEGER, '
        'month INTEGER, '
        'cycle INTEGER)';
  }

  static Future<List<FlowerMonthlyNurtureCycle>> getByFlower(int flowerId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flower_id = ?",
      whereArgs: [flowerId]
    );

    return rows.map((row) => FlowerMonthlyNurtureCycle(
      row['id'],
      row['flower_id'],
      row['type_id'],
      row['month'],
      row['cycle']
    )).toList();
  }

  static Future<List<FlowerMonthlyNurtureCycle>> getByFlowerAndType(int flowerId, int typeId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flower_id = ? AND type_id = ?",
      whereArgs: [flowerId, typeId]
    );

    return rows.map((row) => FlowerMonthlyNurtureCycle(
      row['id'],
      row['flower_id'],
      row['type_id'],
      row['month'],
      row['cycle']
    )).toList();
  }

  // 批量创建某个植物某种养护类型的月份周期数据
  static Future<void> createBatch(int flowerId, int typeId, List<int> monthlyCycles) async {
    final cycleData = MonthlyCycleData(cycles: monthlyCycles);
    await saveFlowerTypeMonthlyData(flowerId, typeId, cycleData);
  }

  // 批量更新某个植物某种养护类型的月份周期数据
  static Future<void> updateBatch(int flowerId, int typeId, List<int> monthlyCycles) async {
    final cycleData = MonthlyCycleData(cycles: monthlyCycles);
    await saveFlowerTypeMonthlyData(flowerId, typeId, cycleData);
  }

  static Future<int> deleteByFlower(int flowerId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'flower_id = ?', whereArgs: [flowerId]);
  }

  static Future<int> deleteByNurtureType(int nurtureTypeId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'type_id = ?', whereArgs: [nurtureTypeId]);
  }

  // 查询某个植物的所有养护类型的月份周期并返回FlowerMonthlyCycles对象
  static Future<FlowerMonthlyCycles> getFlowerMonthlyCycles(int flowerId) async {
    final records = await getByFlower(flowerId);

    final ret = FlowerMonthlyCycles();

    for (final record in records) {
      ret.setCycleForTypeAndMonthById(record.typeId, record.month, record.cycle);
    }

    return ret;
  }

  // 查询某个植物的某种养护类型的月份周期并返回MonthlyCycleData对象
  static Future<MonthlyCycleData> getFlowerTypeMonthlyData(int flowerId, int typeId) async {
    final records = await getByFlowerAndType(flowerId, typeId);

    final ret = MonthlyCycleData.createDefault();

    for (final record in records) {
      ret.setCycleForMonth(record.month, record.cycle);
    }

    return ret;
  }

  // 保存某个植物的所有养护类型的月份周期
  static Future<void> saveFlowerMonthlyCycles(int flowerId, FlowerMonthlyCycles flowerCycles) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 先删除现有数据
    batch.delete(tableName, where: 'flower_id = ?', whereArgs: [flowerId]);

    // 一次性批量插入所有数据
    for (final entry in flowerCycles.typesCycles.entries) {
      final typeId = entry.key;
      final cycleData = entry.value;

      for (int month = 1; month <= 12; month++) {
        final cycle = cycleData.cycles[month - 1];
        // 只保存非继承（非0）的月份数据
        if (cycle != 0) {
          batch.insert(tableName, {
            'flower_id': flowerId,
            'type_id': typeId,
            'month': month,
            'cycle': cycle,
          });
        }
      }
    }

    await batch.commit(noResult: true);
  }

  // 保存某个植物的某种养护类型的月份周期
  static Future<void> saveFlowerTypeMonthlyData(int flowerId, int typeId, MonthlyCycleData cycleData) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 删除该植物该养护类型的现有数据
    batch.delete(
      tableName,
      where: 'flower_id = ? AND type_id = ?',
      whereArgs: [flowerId, typeId],
    );

    // 批量插入新数据（只保存非继承的月份数据）
    for (int month = 1; month <= 12; month++) {
      final cycle = cycleData.cycles[month - 1];
      // 只保存非继承（非0）的月份数据
      if (cycle != 0) {
        batch.insert(tableName, {
          'flower_id': flowerId,
          'type_id': typeId,
          'month': month,
          'cycle': cycle,
        });
      }
    }

    await batch.commit(noResult: true);
  }
}
